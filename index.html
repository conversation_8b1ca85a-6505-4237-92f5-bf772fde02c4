<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示屏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
        }

        .monitor-container {
            position: relative;
        }

        .monitor {
            width: 1200px;
            height: 720px;
            background: #2c3e50;
            border-radius: 8px;
            padding: 8px 8px 25px 8px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .screen {
            width: 100%;
            height: calc(100% - 17px);
            background: #000;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            border: 1px solid #34495e;
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.8);
        }

        .screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.05) 50%,
                transparent 70%
            );
            pointer-events: none;
        }



        .monitor-base {
            width: 300px;
            height: 100px;
            background: linear-gradient(to bottom, #34495e, #2c3e50);
            border-radius: 0 0 20px 20px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .monitor-stand {
            width: 120px;
            height: 60px;
            background: linear-gradient(to bottom, #2c3e50, #1a252f);
            border-radius: 10px;
            margin: 0 auto;
            position: relative;
            top: -10px;
        }

        .power-button {
            width: 8px;
            height: 8px;
            background: #e74c3c;
            border-radius: 50%;
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 6px rgba(231, 76, 60, 0.5);
        }

        .reflection {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 100px;
            height: 60px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.2) 0%,
                transparent 50%
            );
            border-radius: 10px;
            pointer-events: none;
        }

        .table {
            position: absolute;
            top: 80%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 900px;
            height: 120px;
        }

        .table-top {
            width: 100%;
            height: 20px;
            background: linear-gradient(to bottom, #8B4513, #654321);
            border-radius: 8px 8px 4px 4px;
            box-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                inset 0 1px 2px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .table-leg {
            position: absolute;
            width: 12px;
            height: 80px;
            background: linear-gradient(to right, #654321, #4A2C17);
            border-radius: 2px;
            top: 20px;
            box-shadow: 1px 0 2px rgba(0, 0, 0, 0.2);
        }

        .table-leg:nth-child(2) {
            left: 50px;
        }

        .table-leg:nth-child(3) {
            right: 50px;
        }

        .swatter {
            position: absolute;
            top: 35%;
            right: 30%;
            width: 100px;
            height: 140px;
            transform: rotate(-20deg);
        }

        .swatter-handle {
            width: 12px;
            height: 70px;
            background: linear-gradient(to right, #333, #555);
            border-radius: 6px;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 1px 0 2px rgba(0, 0, 0, 0.4);
        }

        .swatter-head {
            width: 80px;
            height: 80px;
            border: 3px solid #333;
            border-radius: 8px;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            background:
                linear-gradient(90deg, transparent 48%, #333 48%, #333 52%, transparent 52%),
                linear-gradient(0deg, transparent 48%, #333 48%, #333 52%, transparent 52%),
                repeating-linear-gradient(90deg, transparent 0px, transparent 8px, #333 8px, #333 10px),
                repeating-linear-gradient(0deg, transparent 0px, transparent 8px, #333 8px, #333 10px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
        }

        .swatter.swatting {
            animation: swat 0.25s ease-in-out;
        }

        @keyframes swat {
            0% {
                top: 35%;
                transform: rotate(-20deg);
            }
            50% {
                top: 65%;
                transform: rotate(-10deg);
            }
            100% {
                top: 35%;
                transform: rotate(-20deg);
            }
        }

        .spider-container {
            position: absolute;
            top: 62%;
            left: 5%;
            width: 60px;
            height: 60px;
            animation: moveLeftRight 8s ease-in-out infinite;
        }

        .character {
            position: relative;
            width: 60px;
            height: 60px;
            background-image: url('孙笑川.png');
            background-size: cover;
            background-position: center;
            border-radius: 50%;
            z-index: 2;
        }

        .spider-legs {
            position: absolute;
            top: 0;
            left: 0;
            width: 60px;
            height: 60px;
            z-index: 1;
            animation: legWalk 2s ease-in-out infinite;
        }

        .leg {
            position: absolute;
            transform-origin: top center;
        }

        .leg-segment {
            background: white;
            border-radius: 50px;
            position: absolute;
        }

        /* 左侧腿部 */
        .leg1 {
            top: 15px;
            left: 8px;
            transform: rotate(-30deg);
        }

        .leg1 .upper {
            width: 3px;
            height: 18px;
            transform-origin: top center;
            transform: rotate(0deg);
        }

        .leg1 .lower {
            width: 3px;
            height: 15px;
            top: 16px;
            left: 0px;
            transform-origin: top center;
            transform: rotate(45deg);
        }

        .leg2 {
            top: 25px;
            left: 5px;
            transform: rotate(-60deg);
        }

        .leg2 .upper {
            width: 3px;
            height: 20px;
            transform: rotate(0deg);
        }

        .leg2 .lower {
            width: 3px;
            height: 18px;
            top: 18px;
            left: 0px;
            transform: rotate(60deg);
        }

        .leg3 {
            top: 35px;
            left: 8px;
            transform: rotate(-120deg);
        }

        .leg3 .upper {
            width: 3px;
            height: 18px;
            transform: rotate(0deg);
        }

        .leg3 .lower {
            width: 3px;
            height: 15px;
            top: 16px;
            left: 0px;
            transform: rotate(45deg);
        }

        /* 右侧腿部 */
        .leg4 {
            top: 15px;
            right: 8px;
            transform: rotate(30deg);
        }

        .leg4 .upper {
            width: 3px;
            height: 18px;
            transform: rotate(0deg);
        }

        .leg4 .lower {
            width: 3px;
            height: 15px;
            top: 16px;
            left: 0px;
            transform: rotate(-45deg);
        }

        .leg5 {
            top: 25px;
            right: 5px;
            transform: rotate(60deg);
        }

        .leg5 .upper {
            width: 3px;
            height: 20px;
            transform: rotate(0deg);
        }

        .leg5 .lower {
            width: 3px;
            height: 18px;
            top: 18px;
            left: 0px;
            transform: rotate(-60deg);
        }

        .leg6 {
            top: 35px;
            right: 8px;
            transform: rotate(120deg);
        }

        .leg6 .upper {
            width: 3px;
            height: 18px;
            transform: rotate(0deg);
        }

        .leg6 .lower {
            width: 3px;
            height: 15px;
            top: 16px;
            left: 0px;
            transform: rotate(-45deg);
        }

        @keyframes moveLeftRight {
            0% {
                left: 15%;
            }
            50% {
                left: 80%;
            }
            100% {
                left: 15%;
            }
        }

        @keyframes legWalk {
            0% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(2deg);
            }
            50% {
                transform: rotate(0deg);
            }
            75% {
                transform: rotate(-2deg);
            }
            100% {
                transform: rotate(0deg);
            }
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="monitor">
            <div class="screen">
                <div class="reflection"></div>
                <div class="table">
                    <div class="table-top"></div>
                    <div class="table-leg"></div>
                    <div class="table-leg"></div>
                </div>
                <div class="swatter">
                    <div class="swatter-head"></div>
                    <div class="swatter-handle"></div>
                </div>
                <div class="spider-container">
                    <div class="spider-legs">
                        <div class="leg leg1">
                            <div class="leg-segment upper"></div>
                            <div class="leg-segment lower"></div>
                        </div>
                        <div class="leg leg2">
                            <div class="leg-segment upper"></div>
                            <div class="leg-segment lower"></div>
                        </div>
                        <div class="leg leg3">
                            <div class="leg-segment upper"></div>
                            <div class="leg-segment lower"></div>
                        </div>
                        <div class="leg leg4">
                            <div class="leg-segment upper"></div>
                            <div class="leg-segment lower"></div>
                        </div>
                        <div class="leg leg5">
                            <div class="leg-segment upper"></div>
                            <div class="leg-segment lower"></div>
                        </div>
                        <div class="leg leg6">
                            <div class="leg-segment upper"></div>
                            <div class="leg-segment lower"></div>
                        </div>
                    </div>
                    <div class="character"></div>
                </div>
            </div>
            <div class="power-button"></div>
        </div>
        <div class="monitor-base">
            <div class="monitor-stand"></div>
        </div>
    </div>

    <script>
        const swatter = document.querySelector('.swatter');
        const screen = document.querySelector('.screen');
        let isSwatting = false; // 标记是否正在拍击

        // 点击屏幕触发拍击动画
        screen.addEventListener('click', function() {
            // 如果正在拍击，则忽略点击
            if (isSwatting) {
                return;
            }

            // 设置拍击状态
            isSwatting = true;

            // 移除之前的动画类（如果存在）
            swatter.classList.remove('swatting');

            // 强制重绘，然后添加动画类
            void swatter.offsetWidth;
            swatter.classList.add('swatting');

            // 动画结束后移除类并重置状态
            setTimeout(() => {
                swatter.classList.remove('swatting');
                isSwatting = false; // 重置状态，允许下次拍击
            }, 250);
        });

        // 也可以通过键盘空格键触发
        document.addEventListener('keydown', function(e) {
            if (e.code === 'Space') {
                e.preventDefault();
                // 只有在不拍击时才触发
                if (!isSwatting) {
                    screen.click(); // 触发点击事件
                }
            }
        });
    </script>
</body>
</html>
