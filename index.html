<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示屏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
        }

        .monitor-container {
            position: relative;
        }

        .monitor {
            width: 1200px;
            height: 720px;
            background: #2c3e50;
            border-radius: 8px;
            padding: 8px 8px 25px 8px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .screen {
            width: 100%;
            height: calc(100% - 17px);
            background: #000;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            border: 1px solid #34495e;
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.8);
        }

        .screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.05) 50%,
                transparent 70%
            );
            pointer-events: none;
        }



        .monitor-base {
            width: 300px;
            height: 100px;
            background: linear-gradient(to bottom, #34495e, #2c3e50);
            border-radius: 0 0 20px 20px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .monitor-stand {
            width: 120px;
            height: 60px;
            background: linear-gradient(to bottom, #2c3e50, #1a252f);
            border-radius: 10px;
            margin: 0 auto;
            position: relative;
            top: -10px;
        }

        .power-button {
            width: 6px;
            height: 6px;
            background: #e74c3c;
            border-radius: 50%;
            position: absolute;
            bottom: 6px;
            right: 15px;
            box-shadow: 0 0 4px rgba(231, 76, 60, 0.5);
        }

        .reflection {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 100px;
            height: 60px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.2) 0%,
                transparent 50%
            );
            border-radius: 10px;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="monitor">
            <div class="screen">
                <div class="reflection"></div>
            </div>
            <div class="power-button"></div>
        </div>
        <div class="monitor-base">
            <div class="monitor-stand"></div>
        </div>
    </div>


</body>
</html>
