<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示屏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
        }

        .monitor-container {
            position: relative;
        }

        .monitor {
            width: 1200px;
            height: 720px;
            background: #2c3e50;
            border-radius: 8px;
            padding: 8px 8px 25px 8px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .screen {
            width: 100%;
            height: calc(100% - 17px);
            background: #000;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            border: 1px solid #34495e;
            box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.8);
        }

        .screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.05) 50%,
                transparent 70%
            );
            pointer-events: none;
        }



        .monitor-base {
            width: 300px;
            height: 100px;
            background: linear-gradient(to bottom, #34495e, #2c3e50);
            border-radius: 0 0 20px 20px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .monitor-stand {
            width: 120px;
            height: 60px;
            background: linear-gradient(to bottom, #2c3e50, #1a252f);
            border-radius: 10px;
            margin: 0 auto;
            position: relative;
            top: -10px;
        }

        .power-button {
            width: 8px;
            height: 8px;
            background: #e74c3c;
            border-radius: 50%;
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 6px rgba(231, 76, 60, 0.5);
        }

        .reflection {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 100px;
            height: 60px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.2) 0%,
                transparent 50%
            );
            border-radius: 10px;
            pointer-events: none;
        }

        .table {
            position: absolute;
            top: 80%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 900px;
            height: 120px;
        }

        .table-top {
            width: 100%;
            height: 20px;
            background: linear-gradient(to bottom, #8B4513, #654321);
            border-radius: 8px 8px 4px 4px;
            box-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                inset 0 1px 2px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .table-leg {
            position: absolute;
            width: 12px;
            height: 80px;
            background: linear-gradient(to right, #654321, #4A2C17);
            border-radius: 2px;
            top: 20px;
            box-shadow: 1px 0 2px rgba(0, 0, 0, 0.2);
        }

        .table-leg:nth-child(2) {
            left: 50px;
        }

        .table-leg:nth-child(3) {
            right: 50px;
        }

        .swatter {
            position: absolute;
            top: 35%;
            right: 20%;
            width: 100px;
            height: 140px;
            transform: rotate(-20deg);
        }

        .swatter-handle {
            width: 12px;
            height: 70px;
            background: linear-gradient(to right, #333, #555);
            border-radius: 6px;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 1px 0 2px rgba(0, 0, 0, 0.4);
        }

        .swatter-head {
            width: 80px;
            height: 80px;
            border: 3px solid #333;
            border-radius: 8px;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            background:
                linear-gradient(90deg, transparent 48%, #333 48%, #333 52%, transparent 52%),
                linear-gradient(0deg, transparent 48%, #333 48%, #333 52%, transparent 52%),
                repeating-linear-gradient(90deg, transparent 0px, transparent 8px, #333 8px, #333 10px),
                repeating-linear-gradient(0deg, transparent 0px, transparent 8px, #333 8px, #333 10px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
        }

        .swatter.swatting {
            animation: swat 0.4s ease-in-out;
        }

        @keyframes swat {
            0% {
                top: 35%;
                transform: rotate(-20deg);
            }
            50% {
                top: 65%;
                transform: rotate(-10deg);
            }
            100% {
                top: 35%;
                transform: rotate(-20deg);
            }
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <div class="monitor">
            <div class="screen">
                <div class="reflection"></div>
                <div class="table">
                    <div class="table-top"></div>
                    <div class="table-leg"></div>
                    <div class="table-leg"></div>
                </div>
                <div class="swatter">
                    <div class="swatter-head"></div>
                    <div class="swatter-handle"></div>
                </div>
            </div>
            <div class="power-button"></div>
        </div>
        <div class="monitor-base">
            <div class="monitor-stand"></div>
        </div>
    </div>

    <script>
        const swatter = document.querySelector('.swatter');
        const screen = document.querySelector('.screen');

        // 点击屏幕触发拍击动画
        screen.addEventListener('click', function() {
            // 移除之前的动画类（如果存在）
            swatter.classList.remove('swatting');

            // 强制重绘，然后添加动画类
            void swatter.offsetWidth;
            swatter.classList.add('swatting');

            // 动画结束后移除类
            setTimeout(() => {
                swatter.classList.remove('swatting');
            }, 400);
        });

        // 也可以通过键盘空格键触发
        document.addEventListener('keydown', function(e) {
            if (e.code === 'Space') {
                e.preventDefault();
                screen.click(); // 触发点击事件
            }
        });
    </script>
</body>
</html>
